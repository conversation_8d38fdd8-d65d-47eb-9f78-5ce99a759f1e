import SwiftUI

// MARK: - Custom Input Field
struct CustomInputField: View {
    @Binding var text: String
    let placeholder: String
    let keyboardType: UIKeyboardType
    let isSecure: Bool
    let leadingIcon: String?
    let trailingIcon: String?
    let onTrailingIconTap: (() -> Void)?
    
    @Environment(\.appColors) var colors
    @FocusState private var isFocused: Bool
    
    init(
        text: Binding<String>,
        placeholder: String,
        keyboardType: UIKeyboardType = .default,
        isSecure: Bool = false,
        leadingIcon: String? = nil,
        trailingIcon: String? = nil,
        onTrailingIconTap: (() -> Void)? = nil
    ) {
        self._text = text
        self.placeholder = placeholder
        self.keyboardType = keyboardType
        self.isSecure = isSecure
        self.leadingIcon = leadingIcon
        self.trailingIcon = trailingIcon
        self.onTrailingIconTap = onTrailingIconTap
    }
    
    var body: some View {
        HStack(spacing: 12.h) {
            // Leading Icon
            if let leadingIcon = leadingIcon {
                Image(systemName: leadingIcon)
                    .font(.system(size: 16))
                    .foregroundColor(colors.secondaryText)
                    .frame(width: 16.h, height: 16.h)
            }
            
            // Text Field
            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                } else {
                    TextField(placeholder, text: $text)
                }
            }
            .keyboardType(keyboardType)
            .focused($isFocused)
            .textFieldStyle(PlainTextFieldStyle())
            .body14Regular()
            .themedTextColor(.primary)
            
            // Trailing Icon
            if let trailingIcon = trailingIcon {
                Button(action: {
                    onTrailingIconTap?()
                }) {
                    Image(systemName: trailingIcon)
                        .font(.system(size: 16))
                        .foregroundColor(colors.secondaryText)
                        .frame(width: 16.h, height: 16.h)
                }
            }
        }
        .padding(.horizontal, 16.h)
        .padding(.vertical, 12.h)
        .background(colors.surface)
        .overlay(
            RoundedRectangle(cornerRadius: 8.h)
                .stroke(borderColor, lineWidth: 1)
        )
        .clipShape(RoundedRectangle(cornerRadius: 8.h))
    }
    
    private var borderColor: Color {
        if isFocused {
            return colors.brandPrimary
        } else {
            return colors.borderDefault
        }
    }
}

// MARK: - Preview
struct CustomInputField_Previews: PreviewProvider {
    @State static var text = ""
    @State static var password = ""
    
    static var previews: some View {
        VStack(spacing: 20) {
            CustomInputField(
                text: $text,
                placeholder: "Enter your email",
                keyboardType: .emailAddress,
                leadingIcon: "envelope"
            )
            
            CustomInputField(
                text: $password,
                placeholder: "Enter password",
                isSecure: true,
                trailingIcon: "eye",
                onTrailingIconTap: { }
            )
        }
        .padding()
        .environmentObject(ThemeManager())
    }
}
