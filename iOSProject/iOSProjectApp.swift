//
//  iOSProjectApp.swift
//  iOSProject
//
//  Created by Apple on 08/08/2025.
//

import SwiftUI

@main
struct iOSProjectApp: App {
    @StateObject private var themeManager = ThemeManager()
    @StateObject private var localizationManager = LocalizationManager()
    @StateObject private var dependencyContainer = DependencyContainer()

    init() {
        // Register custom fonts
        FontRegistration.registerFonts()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(themeManager)
                .environmentObject(localizationManager)
                .environmentObject(dependencyContainer)
                .appTheme(themeManager)
                .onReceive(themeManager.$currentColors) { colors in
                    // Update environment when theme changes
                }
        }
    }
}
