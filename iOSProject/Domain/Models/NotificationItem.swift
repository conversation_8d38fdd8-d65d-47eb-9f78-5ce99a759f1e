import Foundation

// MARK: - Notification Item Model
struct NotificationItem: Codable, Identifiable, Equatable {
    let id: String
    let title: String
    let message: String
    let type: NotificationType
    let isRead: Bool
    let createdAt: Date
    let actionURL: String?
    let imageURL: String?
    
    init(
        id: String = UUID().uuidString,
        title: String,
        message: String,
        type: NotificationType,
        isRead: Bool = false,
        createdAt: Date = Date(),
        actionURL: String? = nil,
        imageURL: String? = nil
    ) {
        self.id = id
        self.title = title
        self.message = message
        self.type = type
        self.isRead = isRead
        self.createdAt = createdAt
        self.actionURL = actionURL
        self.imageURL = imageURL
    }
}

// MARK: - Notification Type
enum NotificationType: String, Codable, CaseIterable {
    case info = "info"
    case warning = "warning"
    case error = "error"
    case success = "success"
    case news = "news"
    case update = "update"
    case promotion = "promotion"
    
    var displayName: String {
        switch self {
        case .info:
            return "Information"
        case .warning:
            return "Warning"
        case .error:
            return "Error"
        case .success:
            return "Success"
        case .news:
            return "News"
        case .update:
            return "Update"
        case .promotion:
            return "Promotion"
        }
    }
    
    var iconName: String {
        switch self {
        case .info:
            return "info.circle"
        case .warning:
            return "exclamationmark.triangle"
        case .error:
            return "xmark.circle"
        case .success:
            return "checkmark.circle"
        case .news:
            return "newspaper"
        case .update:
            return "arrow.clockwise.circle"
        case .promotion:
            return "star.circle"
        }
    }
    
    var color: String {
        switch self {
        case .info:
            return "7E56D8"
        case .warning:
            return "FF9500"
        case .error:
            return "FF5252"
        case .success:
            return "34C759"
        case .news:
            return "52379E"
        case .update:
            return "007AFF"
        case .promotion:
            return "FF2D92"
        }
    }
}

// MARK: - Notification Item Extensions
extension NotificationItem {
    static let mockItems: [NotificationItem] = [
        NotificationItem(
            title: "Welcome to the app!",
            message: "Thank you for joining us. Explore all the amazing features we have to offer.",
            type: .success,
            isRead: false
        ),
        NotificationItem(
            title: "New feature available",
            message: "Check out our latest analytics dashboard with enhanced reporting capabilities.",
            type: .update,
            isRead: false,
            createdAt: Calendar.current.date(byAdding: .hour, value: -2, to: Date()) ?? Date()
        ),
        NotificationItem(
            title: "Portfolio update",
            message: "Your portfolio performance has improved by 15% this month.",
            type: .info,
            isRead: true,
            createdAt: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
        ),
        NotificationItem(
            title: "Special offer",
            message: "Upgrade to premium and get 50% off for the first 3 months.",
            type: .promotion,
            isRead: false,
            createdAt: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date()
        ),
        NotificationItem(
            title: "System maintenance",
            message: "Scheduled maintenance will occur tonight from 2 AM to 4 AM EST.",
            type: .warning,
            isRead: true,
            createdAt: Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date()
        )
    ]
    
    var formattedCreatedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    var timeAgo: String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(createdAt)
        
        if timeInterval < 60 {
            return "Just now"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)m ago"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)h ago"
        } else {
            let days = Int(timeInterval / 86400)
            if days < 7 {
                return "\(days)d ago"
            } else {
                let formatter = DateFormatter()
                formatter.dateFormat = "MMM d"
                return formatter.string(from: createdAt)
            }
        }
    }
}
